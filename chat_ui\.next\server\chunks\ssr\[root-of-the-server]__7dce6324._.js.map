{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatInput.tsx"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { FaPaperPlane } from \"react-icons/fa\";\r\n\r\ninterface ChatInputProps {\r\n  onSendMessage: (message: string) => void;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport default function ChatInput({ onSendMessage, isLoading }: ChatInputProps) {\r\n  const [input, setInput] = useState(\"\");\r\n\r\n  const handleSendMessage = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!input.trim()) return;\r\n    onSendMessage(input);\r\n    setInput(\"\");\r\n  };\r\n\r\n  return (\r\n    <footer className=\"bg-gray-800 border-t border-gray-700 py-4 px-6\">\r\n      <form onSubmit={handleSendMessage} className=\"relative\">\r\n        <Textarea\r\n          placeholder=\"Type your message...\"\r\n          className=\"pr-16 rounded-lg bg-gray-700 border-gray-600 text-white focus:ring-blue-500 focus:border-blue-500\"\r\n          value={input}\r\n          onChange={(e) => setInput(e.target.value)}\r\n          onKeyDown={(e) => {\r\n            if (e.key === \"Enter\" && !e.shiftKey) {\r\n              handleSendMessage(e);\r\n            }\r\n          }}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"absolute top-1/2 right-4 -translate-y-1/2 bg-blue-600 hover:bg-blue-700\"\r\n          disabled={isLoading}\r\n        >\r\n          <FaPaperPlane className=\"w-5 h-5\" />\r\n        </Button>\r\n      </form>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAOe,SAAS,UAAU,EAAE,aAAa,EAAE,SAAS,EAAkB;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;QACnB,cAAc;QACd,SAAS;IACX;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAK,UAAU;YAAmB,WAAU;;8BAC3C,8OAAC,6HAAA,CAAA,WAAQ;oBACP,aAAY;oBACZ,WAAU;oBACV,OAAO;oBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oBACxC,WAAW,CAAC;wBACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;4BACpC,kBAAkB;wBACpB;oBACF;;;;;;8BAEF,8OAAC,2HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;8BAEV,cAAA,8OAAC,8IAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKlC", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatMessage.tsx"], "sourcesContent": ["import { <PERSON>a<PERSON><PERSON><PERSON>, FaUser } from \"react-icons/fa\";\r\nimport ReactMarkdown from \"react-markdown\";\r\nimport { Prism as SyntaxHighlighter } from \"react-syntax-highlighter\";\r\nimport { vscDarkPlus } from \"react-syntax-highlighter/dist/esm/styles/prism\";\r\nimport remarkGfm from \"remark-gfm\";\r\n\r\ninterface ChatMessageProps {\r\n  message: {\r\n    content: string;\r\n    type: \"human\" | \"ai\";\r\n  };\r\n}\r\n\r\nexport default function ChatMessage({ message }: ChatMessageProps) {\r\n  const isHuman = message.type === \"human\";\r\n\r\n  return (\r\n    <div className={`flex items-start gap-4 ${isHuman ? \"justify-end\" : \"\"}`}>\r\n      {!isHuman && (\r\n        <div className=\"rounded-full bg-gray-700 w-8 h-8 flex items-center justify-center\">\r\n          <FaRobot className=\"w-5 h-5 text-gray-400\" />\r\n        </div>\r\n      )}\r\n      <div\r\n        className={`${\r\n          isHuman ? \"bg-blue-600\" : \"bg-gray-700\"\r\n        } rounded-lg p-4 max-w-[75%]`}\r\n      >\r\n        <ReactMarkdown\r\n          remarkPlugins={[remarkGfm]}\r\n          components={{\r\n            code({ node, inline, className, children, ...props }: any) {\r\n              const match = /language-(\\w+)/.exec(className || \"\");\r\n              return !inline && match ? (\r\n                <SyntaxHighlighter\r\n                  style={vscDarkPlus as any}\r\n                  language={match[1]}\r\n                  PreTag=\"div\"\r\n                  {...props}\r\n                >\r\n                  {String(children).replace(/\\n$/, \"\")}\r\n                </SyntaxHighlighter>\r\n              ) : (\r\n                <code className={className} {...props}>\r\n                  {children}\r\n                </code>\r\n              );\r\n            },\r\n          }}\r\n        >\r\n          {message.content}\r\n        </ReactMarkdown>\r\n      </div>\r\n      {isHuman && (\r\n        <div className=\"rounded-full bg-gray-700 w-8 h-8 flex items-center justify-center\">\r\n          <FaUser className=\"w-5 h-5 text-gray-400\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AASe,SAAS,YAAY,EAAE,OAAO,EAAoB;IAC/D,MAAM,UAAU,QAAQ,IAAI,KAAK;IAEjC,qBACE,8OAAC;QAAI,WAAW,CAAC,uBAAuB,EAAE,UAAU,gBAAgB,IAAI;;YACrE,CAAC,yBACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8IAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAGvB,8OAAC;gBACC,WAAW,GACT,UAAU,gBAAgB,cAC3B,2BAA2B,CAAC;0BAE7B,cAAA,8OAAC,wLAAA,CAAA,UAAa;oBACZ,eAAe;wBAAC,6IAAA,CAAA,UAAS;qBAAC;oBAC1B,YAAY;wBACV,MAAK,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY;4BACvD,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;4BACjD,OAAO,CAAC,UAAU,sBAChB,8OAAC,0MAAA,CAAA,QAAiB;gCAChB,OAAO,iPAAA,CAAA,cAAW;gCAClB,UAAU,KAAK,CAAC,EAAE;gCAClB,QAAO;gCACN,GAAG,KAAK;0CAER,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;uDAGnC,8OAAC;gCAAK,WAAW;gCAAY,GAAG,KAAK;0CAClC;;;;;;wBAGP;oBACF;8BAEC,QAAQ,OAAO;;;;;;;;;;;YAGnB,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK5B", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatMessages.tsx"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\r\nimport { FaRobot } from \"react-icons/fa\";\r\nimport ChatMessage from \"./ChatMessage\";\r\nimport ChatLoading from \"./ChatLoading\";\r\n\r\ninterface Message {\r\n  content: string;\r\n  type: \"human\" | \"ai\";\r\n}\r\n\r\ninterface ChatMessagesProps {\r\n  messages: Message[];\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport default function ChatMessages({ messages, isLoading }: ChatMessagesProps) {\r\n  const messagesEndRef = useRef<HTMLDivElement | null>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages, isLoading]);\r\n\r\n  return (\r\n    <main className=\"flex-1 overflow-y-auto p-6\">\r\n      <div className=\"space-y-4\">\r\n        {messages.map((message, index) => (\r\n          <ChatMessage key={index} message={message} />\r\n        ))}\r\n        {isLoading && (\r\n          <div className=\"flex items-start gap-4\">\r\n            <div className=\"rounded-full bg-gray-700 w-8 h-8 flex items-center justify-center\">\r\n              <FaRobot className=\"w-5 h-5 text-gray-400\" />\r\n            </div>\r\n            <div className=\"bg-gray-700 rounded-lg p-4 max-w-[75%] flex items-center gap-2\">\r\n              <ChatLoading className=\"h-5 w-5\" />\r\n              <span>thinking...</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAYe,SAAS,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAqB;IAC7E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAErD,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;;gBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,kIAAA,CAAA,UAAW;wBAAa,SAAS;uBAAhB;;;;;gBAEnB,2BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8IAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,UAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;8BAIZ,8OAAC;oBAAI,KAAK;;;;;;;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatOption.tsx"], "sourcesContent": ["export default function ChatOptions() {\r\n  return (\r\n    <div className=\"flex items-center justify-center h-full gap-2 p-2\">\r\n      {Array.from({ length: 3 }).map((_, index) => (\r\n        <Card key={index} className=\"bg-gray-700 rounded-lg p-4\">\r\n          <p className=\"text-sm\">Option {index + 1}</p>\r\n        </Card>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gBAAiB,WAAU;0BAC1B,cAAA,8OAAC;oBAAE,WAAU;;wBAAU;wBAAQ,QAAQ;;;;;;;eAD9B;;;;;;;;;;AAMnB", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/chat/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { useSendMessageMutation, useGetChatHistoryQuery } from \"@/store/api\";\r\nimport { useParams, useRouter } from \"next/navigation\";\r\nimport ChatInput from \"@/components/chat/ChatInput\";\r\nimport ChatMessages from \"@/components/chat/ChatMessages\";\r\nimport ChatLoading from \"@/components/chat/ChatLoading\";\r\nimport ChatOptions from \"@/components/chat/ChatOption\";\r\n\r\nexport default function Chat() {\r\n  const params = useParams();\r\n  const router = useRouter();\r\n  const id = params.id as string;\r\n  const [messages, setMessages] = useState<\r\n    Array<{ content: string; type: \"human\" | \"ai\" }>\r\n  >([]);\r\n  const [chatId, setChatId] = useState(id === \"new\" ? null : id);\r\n  const [sendMessage, { isLoading: isSending }] = useSendMessageMutation();\r\n  const {\r\n    data: chatHistoryData,\r\n    refetch,\r\n    isLoading: isHistoryLoading,\r\n  } = useGetChatHistoryQuery(chatId!, { skip: !chatId });\r\n\r\n  useEffect(() => {\r\n    if (id === \"new\") {\r\n      setChatId(null);\r\n      setMessages([]);\r\n    } else {\r\n      setChatId(id);\r\n      refetch();\r\n    }\r\n  }, [id, refetch]);\r\n\r\n  useEffect(() => {\r\n    if (chatHistoryData) {\r\n      setMessages(\r\n        chatHistoryData.messages as Array<{\r\n          content: string;\r\n          type: \"human\" | \"ai\";\r\n        }>\r\n      );\r\n    }\r\n  }, [chatHistoryData]);\r\n\r\n  const handleSendMessage = async (message: string) => {\r\n    if (!message.trim()) return;\r\n\r\n    const newMessages = [\r\n      ...messages,\r\n      { content: message, type: \"human\" as const },\r\n    ];\r\n    setMessages(newMessages);\r\n\r\n    if (chatId) {\r\n      const { data } = await sendMessage({ message, chat_id: chatId });\r\n      if (data && data.response) {\r\n        setMessages((prevMessages) => [\r\n          ...prevMessages,\r\n          data.response as unknown as { content: string; type: \"human\" | \"ai\" },\r\n        ]);\r\n      }\r\n    } else {\r\n      const { data } = await sendMessage({ message });\r\n      if (data) {\r\n        router.push(`/dashboard/chat/${data.chat_id}`);\r\n      }\r\n    }\r\n  };\r\n\r\n  if (isHistoryLoading) {\r\n    return (\r\n      <div className=\"flex h-[calc(100vh-3.5rem)] bg-gray-900 text-white items-center justify-center\">\r\n        <ChatLoading />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-[calc(100vh-3.5rem)] bg-gray-900 text-white\">\r\n      <div className=\"flex flex-col flex-1\">\r\n        {messages.length === 0 && <ChatOptions />}\r\n        <ChatMessages messages={messages} isLoading={isSending} />\r\n        <ChatInput onSendMessage={handleSendMessage} isLoading={isSending} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,KAAK,OAAO,EAAE;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAErC,EAAE;IACJ,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,QAAQ,OAAO;IAC3D,MAAM,CAAC,aAAa,EAAE,WAAW,SAAS,EAAE,CAAC,GAAG,CAAA,GAAA,4GAAA,CAAA,yBAAsB,AAAD;IACrE,MAAM,EACJ,MAAM,eAAe,EACrB,OAAO,EACP,WAAW,gBAAgB,EAC5B,GAAG,CAAA,GAAA,4GAAA,CAAA,yBAAsB,AAAD,EAAE,QAAS;QAAE,MAAM,CAAC;IAAO;IAEpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,OAAO;YAChB,UAAU;YACV,YAAY,EAAE;QAChB,OAAO;YACL,UAAU;YACV;QACF;IACF,GAAG;QAAC;QAAI;KAAQ;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,YACE,gBAAgB,QAAQ;QAK5B;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,MAAM,cAAc;eACf;YACH;gBAAE,SAAS;gBAAS,MAAM;YAAiB;SAC5C;QACD,YAAY;QAEZ,IAAI,QAAQ;YACV,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,YAAY;gBAAE;gBAAS,SAAS;YAAO;YAC9D,IAAI,QAAQ,KAAK,QAAQ,EAAE;gBACzB,YAAY,CAAC,eAAiB;2BACzB;wBACH,KAAK,QAAQ;qBACd;YACH;QACF,OAAO;YACL,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,YAAY;gBAAE;YAAQ;YAC7C,IAAI,MAAM;gBACR,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,OAAO,EAAE;YAC/C;QACF;IACF;IAEA,IAAI,kBAAkB;QACpB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,kIAAA,CAAA,UAAW;;;;;;;;;;IAGlB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;gBACZ,SAAS,MAAM,KAAK,mBAAK,8OAAC,iIAAA,CAAA,UAAW;;;;;8BACtC,8OAAC,mIAAA,CAAA,UAAY;oBAAC,UAAU;oBAAU,WAAW;;;;;;8BAC7C,8OAAC,gIAAA,CAAA,UAAS;oBAAC,eAAe;oBAAmB,WAAW;;;;;;;;;;;;;;;;;AAIhE", "debugId": null}}]}