{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/Header.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { SidebarTrigger } from \"./ui/sidebar\";\r\nimport { But<PERSON> } from \"./ui/button\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { PlusIcon } from \"@heroicons/react/24/outline\";\r\n\r\nexport default function Header() {\r\n  const router = useRouter();\r\n\r\n  const handleNewChat = () => {\r\n    router.push('/dashboard/chat/new');\r\n  };\r\n\r\n  return (\r\n    <header className=\"bg-gray-800 py-2 px-4 flex items-center justify-between border-b border-gray-700 text-white fixed w-full z-50 h-14\">\r\n      <SidebarTrigger />\r\n      <Button variant=\"outline\" onClick={handleNewChat} className=\"flex items-center gap-2\">\r\n        <PlusIcon className=\"h-5 w-5\" />\r\n        New Chat\r\n      </Button>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;;;;AAJA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC,4HAAA,CAAA,iBAAc;;;;;0BACf,8OAAC,2HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,SAAS;gBAAe,WAAU;;kCAC1D,8OAAC;wBAAS,WAAU;;;;;;oBAAY;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/layout.tsx"], "sourcesContent": ["\"use client\"\r\nimport Header from \"@/components/Header\";\r\n\r\nexport default function DashboardLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <div className=\"relative\">\r\n      <Header />\r\n      <div className=\"pt-14\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGe,SAAS,gBAAgB,EACtC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,qHAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}