{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatMessage.tsx"], "sourcesContent": ["import { <PERSON>a<PERSON><PERSON><PERSON>, FaUser } from \"react-icons/fa\";\r\nimport ReactMarkdown from \"react-markdown\";\r\nimport { Prism as SyntaxHighlighter } from \"react-syntax-highlighter\";\r\nimport { vscDarkPlus } from \"react-syntax-highlighter/dist/esm/styles/prism\";\r\nimport remarkGfm from \"remark-gfm\";\r\n\r\ninterface ChatMessageProps {\r\n  message: {\r\n    content: string;\r\n    type: \"human\" | \"ai\";\r\n  };\r\n}\r\n\r\nexport default function ChatMessage({ message }: ChatMessageProps) {\r\n  const isHuman = message.type === \"human\";\r\n\r\n  return (\r\n    <div className={`flex items-start gap-4 ${isHuman ? \"justify-end\" : \"\"}`}>\r\n      {!isHuman && (\r\n        <div className=\"rounded-full bg-gray-700 w-8 h-8 flex items-center justify-center\">\r\n          <FaRobot className=\"w-5 h-5 text-gray-400\" />\r\n        </div>\r\n      )}\r\n      <div\r\n        className={`${\r\n          isHuman ? \"bg-blue-600\" : \"bg-gray-700\"\r\n        } rounded-lg p-4 max-w-[75%]`}\r\n      >\r\n        <ReactMarkdown\r\n          remarkPlugins={[remarkGfm]}\r\n          components={{\r\n            code({ node, inline, className, children, ...props }: any) {\r\n              const match = /language-(\\w+)/.exec(className || \"\");\r\n              return !inline && match ? (\r\n                <SyntaxHighlighter\r\n                  style={vscDarkPlus as any}\r\n                  language={match[1]}\r\n                  PreTag=\"div\"\r\n                  {...props}\r\n                >\r\n                  {String(children).replace(/\\n$/, \"\")}\r\n                </SyntaxHighlighter>\r\n              ) : (\r\n                <code className={className} {...props}>\r\n                  {children}\r\n                </code>\r\n              );\r\n            },\r\n          }}\r\n        >\r\n          {message.content}\r\n        </ReactMarkdown>\r\n      </div>\r\n      {isHuman && (\r\n        <div className=\"rounded-full bg-gray-700 w-8 h-8 flex items-center justify-center\">\r\n          <FaUser className=\"w-5 h-5 text-gray-400\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AASe,SAAS,YAAY,KAA6B;QAA7B,EAAE,OAAO,EAAoB,GAA7B;IAClC,MAAM,UAAU,QAAQ,IAAI,KAAK;IAEjC,qBACE,6LAAC;QAAI,WAAW,AAAC,0BAAsD,OAA7B,UAAU,gBAAgB;;YACjE,CAAC,yBACA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAGvB,6LAAC;gBACC,WAAW,AAAC,GAEX,OADC,UAAU,gBAAgB,eAC3B;0BAED,cAAA,6LAAC,2LAAA,CAAA,UAAa;oBACZ,eAAe;wBAAC,gJAAA,CAAA,UAAS;qBAAC;oBAC1B,YAAY;wBACV,MAAK,KAAoD;gCAApD,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY,GAApD;4BACH,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;4BACjD,OAAO,CAAC,UAAU,sBAChB,6LAAC,6MAAA,CAAA,QAAiB;gCAChB,OAAO,oPAAA,CAAA,cAAW;gCAClB,UAAU,KAAK,CAAC,EAAE;gCAClB,QAAO;gCACN,GAAG,KAAK;0CAER,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;uDAGnC,6LAAC;gCAAK,WAAW;gCAAY,GAAG,KAAK;0CAClC;;;;;;wBAGP;oBACF;8BAEC,QAAQ,OAAO;;;;;;;;;;;YAGnB,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK5B;KA/CwB", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatMessages.tsx"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\r\nimport { FaRobot } from \"react-icons/fa\";\r\nimport ChatMessage from \"./ChatMessage\";\r\nimport ChatLoading from \"./ChatLoading\";\r\n\r\ninterface Message {\r\n  content: string;\r\n  type: \"human\" | \"ai\";\r\n}\r\n\r\ninterface ChatMessagesProps {\r\n  messages: Message[];\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport default function ChatMessages({ messages, isLoading }: ChatMessagesProps) {\r\n  const messagesEndRef = useRef<HTMLDivElement | null>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages, isLoading]);\r\n\r\n  return (\r\n    <main className=\"flex-1 overflow-y-auto p-6\">\r\n      <div className=\"space-y-4\">\r\n        {messages.map((message, index) => (\r\n          <ChatMessage key={index} message={message} />\r\n        ))}\r\n        {isLoading && (\r\n          <div className=\"flex items-start gap-4\">\r\n            <div className=\"rounded-full bg-gray-700 w-8 h-8 flex items-center justify-center\">\r\n              <FaRobot className=\"w-5 h-5 text-gray-400\" />\r\n            </div>\r\n            <div className=\"bg-gray-700 rounded-lg p-4 max-w-[75%] flex items-center gap-2\">\r\n              <ChatLoading className=\"h-5 w-5\" />\r\n              <span>thinking...</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAYe,SAAS,aAAa,KAA0C;QAA1C,EAAE,QAAQ,EAAE,SAAS,EAAqB,GAA1C;;IACnC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAErD,MAAM,iBAAiB;YACrB;SAAA,0BAAA,eAAe,OAAO,cAAtB,8CAAA,wBAAwB,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;QAAU;KAAU;IAExB,qBACE,6LAAC;QAAK,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;;gBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,qIAAA,CAAA,UAAW;wBAAa,SAAS;uBAAhB;;;;;gBAEnB,2BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,UAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;8BAIZ,6LAAC;oBAAI,KAAK;;;;;;;;;;;;;;;;;AAIlB;GAhCwB;KAAA", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatOption.tsx"], "sourcesContent": ["import { Card } from \"../ui/card\";\r\nimport { FaScrewdriverWrench } from \"react-icons/fa6\";\r\nimport { LuImagePlus } from \"react-icons/lu\";\r\nimport { FaSignsPost } from \"react-icons/fa6\";\r\nimport Link from \"next/link\";\r\n\r\nconst data = [\r\n  {\r\n    title: \"Scrape Website\",\r\n    description: \"Scrape website data and extract information.\",\r\n    icon: FaScrewdriverWrench,\r\n    param: \"scrape_website\",\r\n  },\r\n  {\r\n    title: \"Generate Image\",\r\n    description: \"Generate Image from text.\",\r\n    icon: LuImagePlus,\r\n    param: \"generate_image\",\r\n  },\r\n  {\r\n    title: \"Generate Poster Images\",\r\n    description: \"Generate poster images from text.\",\r\n    icon: FaSignsPost,\r\n    param: \"generate_poster_images\",\r\n  },\r\n];\r\n\r\nexport default function ChatOptions() {\r\n  return (\r\n    <div className=\"flex items-center justify-center flex-wrap h-full gap-2 p-2\">\r\n      {data.map((item, index) => (\r\n        <Link href={`/dashboard/chat/new?${item.param}`} passHref>\r\n          <Card\r\n            key={index}\r\n            className=\"bg-gray-700 rounded-lg p-4 text-white  gap-3\"\r\n          >\r\n            <item.icon className=\"h-5 w-5\" />\r\n            <p className=\"text-sm\">{item.title}</p>\r\n            <p className=\"text-xs text-white\">{item.description}</p>\r\n          </Card>\r\n        </Link>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;;AAEA,MAAM,OAAO;IACX;QACE,OAAO;QACP,aAAa;QACb,MAAM,kJAAA,CAAA,sBAAmB;QACzB,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,iJAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,kJAAA,CAAA,cAAW;QACjB,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,AAAC,uBAAiC,OAAX,KAAK,KAAK;gBAAI,QAAQ;0BACvD,cAAA,6LAAC,4HAAA,CAAA,OAAI;oBAEH,WAAU;;sCAEV,6LAAC,KAAK,IAAI;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAE,WAAU;sCAAW,KAAK,KAAK;;;;;;sCAClC,6LAAC;4BAAE,WAAU;sCAAsB,KAAK,WAAW;;;;;;;mBAL9C;;;;;;;;;;;;;;;AAWjB;KAjBwB", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/compound/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  createContext,\r\n  useContext,\r\n  useState,\r\n  useEffect,\r\n  ReactNode,\r\n} from \"react\";\r\nimport { useParams, useRouter } from \"next/navigation\";\r\nimport { useSendMessageMutation, useGetChatHistoryQuery } from \"@/store/api\";\r\nimport ChatInput from \"@/components/chat/ChatInput\";\r\nimport ChatMessages from \"@/components/chat/ChatMessages\";\r\nimport ChatLoading from \"@/components/chat/ChatLoading\";\r\nimport ChatOptions from \"@/components/chat/ChatOption\";\r\n\r\ninterface ChatContextType {\r\n  messages: Array<{ content: string; type: \"human\" | \"ai\" }>;\r\n  isSending: boolean;\r\n  isHistoryLoading: boolean;\r\n  handleSendMessage: (message: string) => void;\r\n  chatId: string | null;\r\n}\r\n\r\nconst ChatContext = createContext<ChatContextType | undefined>(undefined);\r\n\r\nexport const useChat = () => {\r\n  const context = useContext(ChatContext);\r\n  if (!context) {\r\n    throw new Error(\"useChat must be used within a ChatProvider\");\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface ChatProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function ChatProvider({ children }: ChatProviderProps) {\r\n  const params = useParams();\r\n  const router = useRouter();\r\n  const id = params.id as string;\r\n  const [messages, setMessages] = useState<\r\n    Array<{ content: string; type: \"human\" | \"ai\" }>\r\n  >([]);\r\n  const [chatId, setChatId] = useState<string | null>(id === \"new\" ? null : id);\r\n  const [sendMessage, { isLoading: isSending }] = useSendMessageMutation();\r\n  const {\r\n    data: chatHistoryData,\r\n    refetch,\r\n    isLoading: isHistoryLoading,\r\n  } = useGetChatHistoryQuery(chatId!, { skip: !chatId });\r\n\r\n  useEffect(() => {\r\n    if (id === \"new\") {\r\n      setChatId(null);\r\n      setMessages([]);\r\n    } else {\r\n      setChatId(id);\r\n      refetch();\r\n    }\r\n  }, [id, refetch]);\r\n\r\n  useEffect(() => {\r\n    if (chatHistoryData) {\r\n      setMessages(\r\n        chatHistoryData.messages as Array<{\r\n          content: string;\r\n          type: \"human\" | \"ai\";\r\n        }>\r\n      );\r\n    }\r\n  }, [chatHistoryData]);\r\n\r\n  const handleSendMessage = async (message: string) => {\r\n    if (!message.trim()) return;\r\n\r\n    const newMessages = [\r\n      ...messages,\r\n      { content: message, type: \"human\" as const },\r\n    ];\r\n    setMessages(newMessages);\r\n\r\n    if (chatId) {\r\n      const { data } = await sendMessage({ message, chat_id: chatId });\r\n      if (data && data.response) {\r\n        setMessages((prevMessages) => [\r\n          ...prevMessages,\r\n          data.response as unknown as { content: string; type: \"human\" | \"ai\" },\r\n        ]);\r\n      }\r\n    } else {\r\n      const { data } = await sendMessage({ message });\r\n      if (data) {\r\n        router.push(`/dashboard/chat/${data.chat_id}`);\r\n      }\r\n    }\r\n  };\r\n\r\n  const value = {\r\n    messages,\r\n    isSending,\r\n    isHistoryLoading,\r\n    handleSendMessage,\r\n    chatId,\r\n  };\r\n\r\n  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;\r\n}\r\n\r\nfunction ChatRoot({ children }: { children: ReactNode }) {\r\n  return (\r\n    <div className=\"flex h-[calc(100vh-3.5rem)] bg-gray-900 text-white\">\r\n      <div className=\"flex flex-col flex-1\">{children}</div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction ChatMessagesComponent() {\r\n  const { messages, isSending } = useChat();\r\n  return <ChatMessages messages={messages} isLoading={isSending} />;\r\n}\r\n\r\nfunction ChatInputComponent() {\r\n  const { handleSendMessage, isSending } = useChat();\r\n  return <ChatInput onSendMessage={handleSendMessage} isLoading={isSending} />;\r\n}\r\n\r\nfunction ChatLoadingComponent() {\r\n  const { isHistoryLoading } = useChat();\r\n  if (!isHistoryLoading) return null;\r\n  return (\r\n    <div className=\"flex h-[calc(100vh-3.5rem)] bg-gray-900 text-white items-center justify-center\">\r\n      <ChatLoading />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction ChatOptionsComponent() {\r\n  const { messages, chatId } = useChat();\r\n  if (messages.length > 0 || chatId) return null;\r\n  return <ChatOptions />;\r\n}\r\n\r\n// Assign the sub-components to the main Chat component\r\nexport const Chat = Object.assign(ChatRoot, {\r\n  Provider: ChatProvider,\r\n  Messages: ChatMessagesComponent,\r\n  Input: ChatInputComponent,\r\n  Loading: ChatLoadingComponent,\r\n  Options: ChatOptionsComponent,\r\n});\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAOA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;AAwBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,SAAS,aAAa,KAA+B;QAA/B,EAAE,QAAQ,EAAqB,GAA/B;;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,KAAK,OAAO,EAAE;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAErC,EAAE;IACJ,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,QAAQ,OAAO;IAC1E,MAAM,CAAC,aAAa,EAAE,WAAW,SAAS,EAAE,CAAC,GAAG,CAAA,GAAA,+GAAA,CAAA,yBAAsB,AAAD;IACrE,MAAM,EACJ,MAAM,eAAe,EACrB,OAAO,EACP,WAAW,gBAAgB,EAC5B,GAAG,CAAA,GAAA,+GAAA,CAAA,yBAAsB,AAAD,EAAE,QAAS;QAAE,MAAM,CAAC;IAAO;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,OAAO,OAAO;gBAChB,UAAU;gBACV,YAAY,EAAE;YAChB,OAAO;gBACL,UAAU;gBACV;YACF;QACF;iCAAG;QAAC;QAAI;KAAQ;IAEhB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,iBAAiB;gBACnB,YACE,gBAAgB,QAAQ;YAK5B;QACF;iCAAG;QAAC;KAAgB;IAEpB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,MAAM,cAAc;eACf;YACH;gBAAE,SAAS;gBAAS,MAAM;YAAiB;SAC5C;QACD,YAAY;QAEZ,IAAI,QAAQ;YACV,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,YAAY;gBAAE;gBAAS,SAAS;YAAO;YAC9D,IAAI,QAAQ,KAAK,QAAQ,EAAE;gBACzB,YAAY,CAAC,eAAiB;2BACzB;wBACH,KAAK,QAAQ;qBACd;YACH;QACF,OAAO;YACL,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,YAAY;gBAAE;YAAQ;YAC7C,IAAI,MAAM;gBACR,OAAO,IAAI,CAAC,AAAC,mBAA+B,OAAb,KAAK,OAAO;YAC7C;QACF;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;IAtEgB;;QACC,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QAMwB,+GAAA,CAAA,yBAAsB;QAKlE,+GAAA,CAAA,yBAAsB;;;KAbZ;AAwEhB,SAAS,SAAS,KAAqC;QAArC,EAAE,QAAQ,EAA2B,GAArC;IAChB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBAAwB;;;;;;;;;;;AAG7C;MANS;AAQT,SAAS;;IACP,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAChC,qBAAO,6LAAC,sIAAA,CAAA,UAAY;QAAC,UAAU;QAAU,WAAW;;;;;;AACtD;IAHS;;QACyB;;;MADzB;AAKT,SAAS;;IACP,MAAM,EAAE,iBAAiB,EAAE,SAAS,EAAE,GAAG;IACzC,qBAAO,6LAAC,mIAAA,CAAA,UAAS;QAAC,eAAe;QAAmB,WAAW;;;;;;AACjE;IAHS;;QACkC;;;MADlC;AAKT,SAAS;;IACP,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,IAAI,CAAC,kBAAkB,OAAO;IAC9B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,qIAAA,CAAA,UAAW;;;;;;;;;;AAGlB;IARS;;QACsB;;;MADtB;AAUT,SAAS;;IACP,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IAC7B,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,OAAO;IAC1C,qBAAO,6LAAC,oIAAA,CAAA,UAAW;;;;;AACrB;IAJS;;QACsB;;;MADtB;AAOF,MAAM,OAAO,OAAO,MAAM,CAAC,UAAU;IAC1C,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,SAAS;AACX", "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/chat/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Chat } from \"@/components/chat/compound\";\r\n\r\nexport default function ChatPage() {\r\n  return (\r\n    <Chat.Provider>\r\n      <Chat>\r\n        <Chat.Loading />\r\n        <Chat.Options />\r\n        <Chat.Messages />\r\n        <Chat.Input />\r\n      </Chat>\r\n    </Chat.Provider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC,2IAAA,CAAA,OAAI,CAAC,QAAQ;kBACZ,cAAA,6LAAC,2IAAA,CAAA,OAAI;;8BACH,6LAAC,2IAAA,CAAA,OAAI,CAAC,OAAO;;;;;8BACb,6LAAC,2IAAA,CAAA,OAAI,CAAC,OAAO;;;;;8BACb,6LAAC,2IAAA,CAAA,OAAI,CAAC,QAAQ;;;;;8BACd,6LAAC,2IAAA,CAAA,OAAI,CAAC,KAAK;;;;;;;;;;;;;;;;AAInB;KAXwB", "debugId": null}}]}