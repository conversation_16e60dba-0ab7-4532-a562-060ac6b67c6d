{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatInput.tsx"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { FaPaperPlane } from \"react-icons/fa\";\r\n\r\ninterface ChatInputProps {\r\n  onSendMessage: (message: string) => void;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport default function ChatInput({ onSendMessage, isLoading }: ChatInputProps) {\r\n  const [input, setInput] = useState(\"\");\r\n\r\n  const handleSendMessage = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!input.trim()) return;\r\n    onSendMessage(input);\r\n    setInput(\"\");\r\n  };\r\n\r\n  return (\r\n    <footer className=\"bg-gray-800 border-t border-gray-700 py-4 px-6\">\r\n      <form onSubmit={handleSendMessage} className=\"relative\">\r\n        <Textarea\r\n          placeholder=\"Type your message...\"\r\n          className=\"pr-16 rounded-lg bg-gray-700 border-gray-600 text-white focus:ring-blue-500 focus:border-blue-500\"\r\n          value={input}\r\n          onChange={(e) => setInput(e.target.value)}\r\n          onKeyDown={(e) => {\r\n            if (e.key === \"Enter\" && !e.shiftKey) {\r\n              handleSendMessage(e);\r\n            }\r\n          }}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"absolute top-1/2 right-4 -translate-y-1/2 bg-blue-600 hover:bg-blue-700\"\r\n          disabled={isLoading}\r\n        >\r\n          <FaPaperPlane className=\"w-5 h-5\" />\r\n        </Button>\r\n      </form>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAOe,SAAS,UAAU,KAA4C;QAA5C,EAAE,aAAa,EAAE,SAAS,EAAkB,GAA5C;;IAChC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;QACnB,cAAc;QACd,SAAS;IACX;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAK,UAAU;YAAmB,WAAU;;8BAC3C,6LAAC,gIAAA,CAAA,WAAQ;oBACP,aAAY;oBACZ,WAAU;oBACV,OAAO;oBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oBACxC,WAAW,CAAC;wBACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;4BACpC,kBAAkB;wBACpB;oBACF;;;;;;8BAEF,6LAAC,8HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;8BAEV,cAAA,6LAAC,iJAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKlC;GAlCwB;KAAA", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatMessage.tsx"], "sourcesContent": ["import { <PERSON>a<PERSON><PERSON><PERSON>, FaUser } from \"react-icons/fa\";\r\nimport ReactMarkdown from \"react-markdown\";\r\nimport { Prism as SyntaxHighlighter } from \"react-syntax-highlighter\";\r\nimport { vscDarkPlus } from \"react-syntax-highlighter/dist/esm/styles/prism\";\r\nimport remarkGfm from \"remark-gfm\";\r\n\r\ninterface ChatMessageProps {\r\n  message: {\r\n    content: string;\r\n    type: \"human\" | \"ai\";\r\n  };\r\n}\r\n\r\nexport default function ChatMessage({ message }: ChatMessageProps) {\r\n  const isHuman = message.type === \"human\";\r\n\r\n  return (\r\n    <div className={`flex items-start gap-4 ${isHuman ? \"justify-end\" : \"\"}`}>\r\n      {!isHuman && (\r\n        <div className=\"rounded-full bg-gray-700 w-8 h-8 flex items-center justify-center\">\r\n          <FaRobot className=\"w-5 h-5 text-gray-400\" />\r\n        </div>\r\n      )}\r\n      <div\r\n        className={`${\r\n          isHuman ? \"bg-blue-600\" : \"bg-gray-700\"\r\n        } rounded-lg p-4 max-w-[75%]`}\r\n      >\r\n        <ReactMarkdown\r\n          remarkPlugins={[remarkGfm]}\r\n          components={{\r\n            code({ node, inline, className, children, ...props }: any) {\r\n              const match = /language-(\\w+)/.exec(className || \"\");\r\n              return !inline && match ? (\r\n                <SyntaxHighlighter\r\n                  style={vscDarkPlus as any}\r\n                  language={match[1]}\r\n                  PreTag=\"div\"\r\n                  {...props}\r\n                >\r\n                  {String(children).replace(/\\n$/, \"\")}\r\n                </SyntaxHighlighter>\r\n              ) : (\r\n                <code className={className} {...props}>\r\n                  {children}\r\n                </code>\r\n              );\r\n            },\r\n          }}\r\n        >\r\n          {message.content}\r\n        </ReactMarkdown>\r\n      </div>\r\n      {isHuman && (\r\n        <div className=\"rounded-full bg-gray-700 w-8 h-8 flex items-center justify-center\">\r\n          <FaUser className=\"w-5 h-5 text-gray-400\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AASe,SAAS,YAAY,KAA6B;QAA7B,EAAE,OAAO,EAAoB,GAA7B;IAClC,MAAM,UAAU,QAAQ,IAAI,KAAK;IAEjC,qBACE,6LAAC;QAAI,WAAW,AAAC,0BAAsD,OAA7B,UAAU,gBAAgB;;YACjE,CAAC,yBACA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAGvB,6LAAC;gBACC,WAAW,AAAC,GAEX,OADC,UAAU,gBAAgB,eAC3B;0BAED,cAAA,6LAAC,2LAAA,CAAA,UAAa;oBACZ,eAAe;wBAAC,gJAAA,CAAA,UAAS;qBAAC;oBAC1B,YAAY;wBACV,MAAK,KAAoD;gCAApD,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY,GAApD;4BACH,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;4BACjD,OAAO,CAAC,UAAU,sBAChB,6LAAC,6MAAA,CAAA,QAAiB;gCAChB,OAAO,oPAAA,CAAA,cAAW;gCAClB,UAAU,KAAK,CAAC,EAAE;gCAClB,QAAO;gCACN,GAAG,KAAK;0CAER,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;uDAGnC,6LAAC;gCAAK,WAAW;gCAAY,GAAG,KAAK;0CAClC;;;;;;wBAGP;oBACF;8BAEC,QAAQ,OAAO;;;;;;;;;;;YAGnB,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK5B;KA/CwB", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatMessages.tsx"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\r\nimport { FaRobot } from \"react-icons/fa\";\r\nimport ChatMessage from \"./ChatMessage\";\r\nimport ChatLoading from \"./ChatLoading\";\r\n\r\ninterface Message {\r\n  content: string;\r\n  type: \"human\" | \"ai\";\r\n}\r\n\r\ninterface ChatMessagesProps {\r\n  messages: Message[];\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport default function ChatMessages({ messages, isLoading }: ChatMessagesProps) {\r\n  const messagesEndRef = useRef<HTMLDivElement | null>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages, isLoading]);\r\n\r\n  return (\r\n    <main className=\"flex-1 overflow-y-auto p-6\">\r\n      <div className=\"space-y-4\">\r\n        {messages.map((message, index) => (\r\n          <ChatMessage key={index} message={message} />\r\n        ))}\r\n        {isLoading && (\r\n          <div className=\"flex items-start gap-4\">\r\n            <div className=\"rounded-full bg-gray-700 w-8 h-8 flex items-center justify-center\">\r\n              <FaRobot className=\"w-5 h-5 text-gray-400\" />\r\n            </div>\r\n            <div className=\"bg-gray-700 rounded-lg p-4 max-w-[75%] flex items-center gap-2\">\r\n              <ChatLoading className=\"h-5 w-5\" />\r\n              <span>thinking...</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAYe,SAAS,aAAa,KAA0C;QAA1C,EAAE,QAAQ,EAAE,SAAS,EAAqB,GAA1C;;IACnC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAErD,MAAM,iBAAiB;YACrB;SAAA,0BAAA,eAAe,OAAO,cAAtB,8CAAA,wBAAwB,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;QAAU;KAAU;IAExB,qBACE,6LAAC;QAAK,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;;gBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,qIAAA,CAAA,UAAW;wBAAa,SAAS;uBAAhB;;;;;gBAEnB,2BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,UAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;8BAIZ,6LAAC;oBAAI,KAAK;;;;;;;;;;;;;;;;;AAIlB;GAhCwB;KAAA", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatOption.tsx"], "sourcesContent": ["import { Card, CardContent, CardDescription, CardHeader } from \"../ui/card\";\r\nimport { FaScrewdriverWrench } from \"react-icons/fa6\";\r\nimport { LuImagePlus } from \"react-icons/lu\";\r\nimport { FaSignsPost } from \"react-icons/fa6\";\r\nimport Link from \"next/link\";\r\n\r\nconst data = [\r\n  {\r\n    title: \"Scrape Website\",\r\n    description: \"Scrape website data and extract information.\",\r\n    icon: FaScrewdriverWrench,\r\n    param: \"scrape_website\",\r\n  },\r\n  {\r\n    title: \"Generate Image\",\r\n    description: \"Generate Image from text.\",\r\n    icon: LuImagePlus,\r\n    param: \"generate_image\",\r\n  },\r\n  {\r\n    title: \"Generate Poster Images\",\r\n    description: \"Generate poster images from text.\",\r\n    icon: FaSignsPost,\r\n    param: \"generate_poster_images\",\r\n  },\r\n];\r\n\r\nexport default function ChatOptions() {\r\n  return (\r\n    <div className=\"flex items-center justify-center flex-wrap h-full gap-2 p-2\">\r\n      {data.map((item, index) => (\r\n        <Link href={`/dashboard/chat/new?${item.param}`} passHref>\r\n          <Card\r\n            key={index}\r\n            className=\"bg-gray-700 rounded-lg p-4 text-white  gap-3\"\r\n          >\r\n            <item.icon className=\"h-5 w-5\" />\r\n            <p className=\"text-sm\">{item.title}</p>\r\n            <p className=\"text-xs text-white\">{item.description}</p>\r\n          </Card>\r\n        </Link>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;;AAEA,MAAM,OAAO;IACX;QACE,OAAO;QACP,aAAa;QACb,MAAM,kJAAA,CAAA,sBAAmB;QACzB,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,iJAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,kJAAA,CAAA,cAAW;QACjB,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,AAAC,uBAAiC,OAAX,KAAK,KAAK;gBAAI,QAAQ;0BACvD,cAAA,6LAAC,4HAAA,CAAA,OAAI;oBAEH,WAAU;;sCAEV,6LAAC,KAAK,IAAI;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAE,WAAU;sCAAW,KAAK,KAAK;;;;;;sCAClC,6LAAC;4BAAE,WAAU;sCAAsB,KAAK,WAAW;;;;;;;mBAL9C;;;;;;;;;;;;;;;AAWjB;KAjBwB", "debugId": null}}]}